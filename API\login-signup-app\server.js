require('dotenv').config();
const express = require('express');
const mongoose = require('mongoose');
const session = require('express-session');
const bcrypt = require('bcrypt');
const path = require('path');
const passport = require('passport');
const User = require('./models/User');

const app = express();

// Middleware
app.use(express.urlencoded({ extended: true }));
app.use(express.static('public'));
app.set('view engine', 'ejs');

// Session
app.use(session({
    secret: process.env.SESSION_SECRET || 'rtffftcdd',
    resave: false,
    saveUninitialized: true
}));

// Passport configuration
require('./auth/google')(passport);

// Passport middleware
app.use(passport.initialize());
app.use(passport.session());

// Passport serialize/deserialize user
passport.serializeUser((user, done) => {
    done(null, user._id);
});

passport.deserializeUser(async (id, done) => {
    try {
        const user = await User.findById(id);
        done(null, user);
    } catch (error) {
        done(error, null);
    }
});

// MongoDB bağlantısı
mongoose.connect(process.env.MONGODB_URI || 'mongodb://127.0.0.1:27017/grumbleDB', {
    useNewUrlParser: true,
    useUnifiedTopology: true
})
.then(() => console.log('MongoDB bağlıdır ✅'))
.catch(err => console.log('Bağlantı xətası ❌', err));

// GET: Signup
app.get('/signup', (req, res) => {
    res.render('signup');
});

// POST: Signup
app.post('/signup', async (req, res) => {
    const { name, email, password } = req.body;
    const hashedPassword = await bcrypt.hash(password, 10);
    try {
        const newUser = new User({ name, email, password: hashedPassword });
        await newUser.save();
        res.redirect('/login');
    } catch (error) {
        res.send('Xəta baş verdi: ' + error.message);
    }
});

// GET: Login
app.get('/login', (req, res) => {
    res.render('login', { error: null });
});

// POST: Login
app.post('/login', async (req, res) => {
    const { email, password } = req.body;
    const user = await User.findOne({ email });

    if (user && user.password && await bcrypt.compare(password, user.password)) {
        req.session.userId = user._id;
        res.redirect('/dashboard');
    } else {
        res.render('login', { error: 'Email və ya şifrə yanlışdır ❌' });
    }
});

app.get('/forgot-password', (req, res) => {
  res.render('forgot-password'); // forgot-password.ejs olmalıdır
});

// Google OAuth routes
app.get('/auth/google',
    passport.authenticate('google', { scope: ['profile', 'email'] })
);

app.get('/auth/google/callback',
    passport.authenticate('google', { failureRedirect: '/login' }),
    (req, res) => {
        // Successful authentication, redirect to dashboard or home
        req.session.userId = req.user._id;
        res.redirect('/dashboard'); // You can change this to wherever you want to redirect after login
    }
);

// Dashboard route (protected)
app.get('/dashboard', async (req, res) => {
    if (!req.session.userId && !req.user) {
        return res.redirect('/login');
    }

    let user = req.user;
    if (!user && req.session.userId) {
        user = await User.findById(req.session.userId);
    }

    res.render('dashboard', { user });
});

// Logout route
app.get('/logout', (req, res) => {
    req.session.destroy((err) => {
        if (err) {
            console.error('Session destroy error:', err);
        }
        res.redirect('/login');
    });
});

// Start
app.listen(3000, () => {
    console.log('🌐 Server is running at http://localhost:3000');
});
