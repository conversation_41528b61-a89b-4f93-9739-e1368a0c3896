# Grumble Authentication System

This is the authentication system for the Grumble application with Google OAuth integration.

## Features

- **Local Authentication**: Email/password based registration and login
- **Google OAuth**: Sign in with Google account
- **Session Management**: Secure session handling with express-session
- **User Management**: MongoDB-based user storage with Mongoose
- **Responsive UI**: Bootstrap-based responsive design

## Google OAuth Setup

### Client Configuration
- **Client ID**: `************-e8rmim5ksg6332al2bnlseqvmbh2atud.apps.googleusercontent.com`
- **Callback URL**: `http://localhost:3000/auth/google/callback`

### Required Environment Variables

Create a `.env` file in the root directory with:

```env
GOOGLE_CLIENT_SECRET=YOUR_GOOGLE_CLIENT_SECRET_HERE
MONGODB_URI=mongodb://127.0.0.1:27017/grumbleDB
SESSION_SECRET=your_session_secret_here
```

### Google Cloud Console Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the Google+ API
4. Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client IDs"
5. Set application type to "Web application"
6. Add authorized redirect URIs:
   - `http://localhost:3000/auth/google/callback`
   - `https://yourdomain.com/auth/google/callback` (for production)
7. Copy the Client Secret and add it to your `.env` file

## Installation & Setup

1. Install dependencies:
```bash
npm install
```

2. Make sure MongoDB is running on your system

3. Set up your `.env` file with the required variables

4. Start the server:
```bash
node server.js
```

5. Visit `http://localhost:3000/login` to test the authentication

## Routes

- `GET /login` - Login page
- `POST /login` - Handle local login
- `GET /signup` - Registration page  
- `POST /signup` - Handle registration
- `GET /auth/google` - Initiate Google OAuth
- `GET /auth/google/callback` - Google OAuth callback
- `GET /dashboard` - Protected dashboard (requires authentication)
- `GET /logout` - Logout and destroy session

## User Model

The User model supports both local and OAuth users:

```javascript
{
  name: String,
  email: String (unique),
  password: String (optional for OAuth users),
  googleId: String (unique, sparse),
  provider: String (default: 'local'),
  avatar: String (profile picture URL),
  createdAt: Date,
  updatedAt: Date
}
```

## Security Features

- Password hashing with bcrypt
- Secure session management
- Environment variable configuration
- OAuth 2.0 implementation
- Input validation and error handling

## Testing

1. **Local Authentication**: Create an account with email/password
2. **Google OAuth**: Click "Google" button on login page
3. **Session Persistence**: Navigate between protected routes
4. **Logout**: Test session destruction

## Notes

- The Google Client Secret must be obtained from Google Cloud Console
- MongoDB must be running for the application to work
- The application uses EJS templating engine
- Bootstrap 5 is used for styling
